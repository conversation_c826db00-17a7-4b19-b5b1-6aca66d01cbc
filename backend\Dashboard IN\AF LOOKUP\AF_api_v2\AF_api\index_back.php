<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="logo.png">
    <link rel="stylesheet" href="style.css">
    <title>AF</title>
</head>

<body>
    <div class="wrapper">
        <div class="container">
            <h1>AF</h1>
            <form method="post" action="">

                <label for="MSISDN">MSISDN:</label>
                <input type="text" name="MSISDN" required>

                <input type="submit" name="submit" value="Submit">
            </form>
        </div>
        <div class="container2">

            <?php
		echo shell_exec('whoami');
            if ($_SERVER["REQUEST_METHOD"] == "POST") {
                $MSISDN = $_POST["MSISDN"];
                if (preg_match('/^\d{8}$/', $MSISDN)) {
                    // executing the shell commands
                    #echo 'ssh -o LogLevel=error ********** "/root/a 216' . $MSISDN . '" |tail -3 |head -1';
		   $output = shell_exec('sudo sh ./test_af.sh '.$MSISDN);
                   #$output = shell_exec('ssh -o UserKnownHostsFile=/dev/null -o StrictHostKeyChecking=no -o LogLevel=quiet ********** "/root/a 216' . $MSISDN . '" |tail -3 |head -1');
                    echo "AF de la ligne " . $MSISDN . " est " . $output;
                } else {
                    // not an 8 digit number, display fail message
                    echo "<p>Invalid input. Please enter an 8-digit number.</p>";
                }
            }
            ?>
        </div>
    </div>

<footer class="page-footer" style="transform:translate(0px,10px)">
           &copy; 2024 AF. All Rights Reserved | Design by DRS <a href="http://village.orangetunisie.intra/"><img src="
img/orange.png" style="width:15px; transform:translate(5px,-02px)"></a>
</footer>
</body>


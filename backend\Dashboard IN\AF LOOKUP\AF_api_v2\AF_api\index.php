<?php
session_start();

// Vérifier l'authentification
if (!isset($_SESSION['authenticated'])) {
    if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST['login']) && isset($_POST['password'])) {
        if ($_POST['login'] === 'supportin' && $_POST['password'] === 'SupportIN@@@2023') {
            $_SESSION['authenticated'] = true;
            header("Location: index.php");
            exit;
        } else {
            $error = "Identifiants incorrects";
        }
    }
    ?>
    <form method="post" style="width: 300px; margin: 100px auto; padding: 20px; border: 1px solid #ccc; text-align: center;">
        <h2>Authentification AF LOOKUP</h2>
        <?php if (isset($error)) echo "<p style='color: red;'>$error</p>"; ?>
        <label>Login:</label>
        <input type="text" name="login" required><br><br>
        <label>Mot de passe:</label>
        <input type="password" name="password" required><br><br>
        <input type="submit" value="Se connecter">
    </form>
    <?php
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="logo.png">
    <title>AF</title>
    <style>
        body { font-family: Arial, sans-serif; background-color: #fff3e0; color: #333; text-align: center; }
        .wrapper { display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100vh; }
        .container { background: #ff9800; padding: 20px; border-radius: 10px; margin-bottom: 20px; }
        input[type=text], input[type=password] { padding: 8px; width: 200px; margin: 5px; }
        input[type=submit] { background: #d84315; color: white; border: none; padding: 10px 20px; cursor: pointer; }
        input[type=submit]:hover { background: #bf360c; }
        footer { position: fixed; bottom: 10px; width: 100%; text-align: center; font-size: 14px; }
    </style>
</head>
<body>
    <div class="wrapper">
        <div class="container">
            <h1>AF</h1>
            <form method="post" action="">
                <label for="MSISDN">MSISDN:</label>
                <input type="text" name="MSISDN" required>
                <input type="submit" name="submit" value="Submit">
            </form>
        </div>
        <div class="container2">
            <?php
            echo shell_exec('whoami');
            if ($_SERVER["REQUEST_METHOD"] == "POST" && isset($_POST["MSISDN"])) {
                $MSISDN = $_POST["MSISDN"];
                if (preg_match('/^\d{8}$/', $MSISDN)) {
                    $output = shell_exec('sudo sh ./test_af.sh ' . escapeshellarg($MSISDN));
                    echo "AF de la ligne " . htmlspecialchars($MSISDN) . " est " . htmlspecialchars($output);
                } else {
                    echo "<p>Invalid input. Please enter an 8-digit number.</p>";
                }
            }
            ?>
        </div>
    </div>

    <footer>
        &copy; 2024 AF. All Rights Reserved | Design by DRS
        <a href="http://village.orangetunisie.intra/"><img src="img/orange.png" style="width:15px;"></a>
    </footer>
</body>
</html>


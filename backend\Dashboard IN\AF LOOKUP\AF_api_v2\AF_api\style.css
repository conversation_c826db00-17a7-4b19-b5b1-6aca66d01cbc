
body {
    font-family: Arial, sans-serif;
    background-color: #ffffff;
    margin: 0;
    padding: 0;
    display: flex;
    justify-content: center; 
    align-items: center; 
    min-height: 100vh; 
    background-image: url("logo.png);
    background-color: #cccccc;
}
.wrapper {
    max-width: 600px;
    background-color: #857c75;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.container {
    max-width: 600px;
    background-color: #FF7900;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.container2 {
    margin-top: 20px;
    max-width: 600px;
    background-color: #FF7900;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
.date_picker{
    margin-top: 5px;
    max-width: 600px;
    background-color: #b08a68;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}


h1 {
    text-align: center;
    color: #333;
}

h3{
    margin-top: 0px;
}
form {
    display: flex;
    flex-direction: column;
}

label {
    margin-bottom: 3px;
}

input[type="text"],
select {
    width: 100%;
    padding: 8px;
    margin-bottom: 8px;
    box-sizing: border-box;
}

input[type="submit"] {
    background-color: #4caf50;
    color: #fff;
    padding: 10px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
}

input[type="submit"]:hover {
    background-color: #45a049;
}

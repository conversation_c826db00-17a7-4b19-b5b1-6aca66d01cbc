<div class="login-container">
  <div class="login-card">
    <img src="assets/orange-tunisie.png" alt="Orange logo" class="logo" />
    <div class="login-header">
      <h2>Welcome</h2>
      <p>Welcome to Orange Tunisia Dashboard</p>
    </div>

    <form (ngSubmit)="onSubmit()" class="login-form">
      <div class="form-group">
        <label for="email">Email</label>
        <div class="input-wrapper">
          <span class="input-icon">
            <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M2.5 5.833A2.5 2.5 0 0 1 5 3.333h10a2.5 2.5 0 0 1 2.5 2.5v8.334a2.5 2.5 0 0 1-2.5 2.5H5a2.5 2.5 0 0 1-2.5-2.5V5.833Zm0 0L10 11.25l7.5-5.417" stroke="#ff7900" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
          </span>
          <input
            type="email"
            id="email"
            name="email"
            [(ngModel)]="email"
            placeholder="Enter your email"
            required
            class="form-control"
            autocomplete="username"
          />
        </div>
      </div>

      <div class="form-group">
        <label for="password">Password</label>
        <div class="input-wrapper">
          <span class="input-icon">
            <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M10 14.167a4.167 4.167 0 1 0 0-8.334 4.167 4.167 0 0 0 0 8.334Z" stroke="#ff7900" stroke-width="1.5"/><path d="M2.5 10c1.667-3.333 5-5.833 7.5-5.833s5.833 2.5 7.5 5.833c-1.667 3.333-5 5.833-7.5 5.833S4.167 13.333 2.5 10Z" stroke="#ff7900" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
          </span>
          <input
            [type]="showPassword ? 'text' : 'password'"
            id="password"
            name="password"
            [(ngModel)]="password"
            placeholder="Enter your password"
            required
            class="form-control"
            autocomplete="current-password"
          />
          <button type="button" class="toggle-password" (click)="togglePasswordVisibility()" tabindex="-1">
            <span *ngIf="!showPassword">
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M2.5 10c1.667-3.333 5-5.833 7.5-5.833s5.833 2.5 7.5 5.833c-1.667 3.333-5 5.833-7.5 5.833S4.167 13.333 2.5 10Z" stroke="#ff7900" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M10 13.333a3.333 3.333 0 1 0 0-6.666 3.333 3.333 0 0 0 0 6.666Z" stroke="#ff7900" stroke-width="1.5"/></svg>
            </span>
            <span *ngIf="showPassword">
              <svg width="20" height="20" fill="none" viewBox="0 0 20 20"><path d="M2.5 10c1.667-3.333 5-5.833 7.5-5.833s5.833 2.5 7.5 5.833c-1.667 3.333-5 5.833-7.5 5.833S4.167 13.333 2.5 10Z" stroke="#ff7900" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M7.5 12.5l5-5M12.5 12.5l-5-5" stroke="#ff7900" stroke-width="1.5" stroke-linecap="round"/></svg>
            </span>
          </button>
        </div>
      </div>

      <div *ngIf="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>

      <button
        type="submit"
        [disabled]="isLoading"
        class="login-button"
      >
        <span *ngIf="!isLoading">Sign In</span>
        <span *ngIf="isLoading">Signing In...</span>
      </button>
      <div class="forgot-password">
        <a (click)="onForgotPassword()" href="javascript:void(0)">Forgot password?</a>
      </div>
    </form>

    <div class="login-footer">
      <p>Don't have an account? <a routerLink="/register">Sign up</a></p>
    </div>

    <div class="demo-credentials">
      <p><strong>Demo Credentials:</strong></p>
      <p>Email: admin&#64;example.com</p>
      <p>Password: password</p>
    </div>
  </div>
</div> 
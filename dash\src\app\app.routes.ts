import { Routes } from '@angular/router';
import { LoginComponent } from './components/login/login.component';
import { RegisterComponent } from './components/register/register.component';
import { DashboardComponent } from './components/dashboard/dashboard.component';
import { DashboardHomeComponent } from './components/dashboard-home/dashboard-home.component';
import { AfLookupComponent } from './components/af-lookup/af-lookup.component';
import { UpdateBalanceComponent } from './components/update-balance/update-balance.component';
import { UpdateFafComponent } from './components/update-faf/update-faf.component';
import { SetGraceDateComponent } from './components/set-grace-date/set-grace-date.component';
import { UpdateUaComponent } from './components/update-ua/update-ua.component';
import { UpdateCugComponent } from './components/update-cug/update-cug.component';
import { AuthGuard } from './guards/auth.guard';

export const routes: Routes = [
  { path: '', redirectTo: '/dashboard', pathMatch: 'full' },
  { path: 'login', component: LoginComponent },
  { path: 'register', component: RegisterComponent },
  {
    path: 'dashboard',
    component: DashboardComponent,
    canActivate: [AuthGuard],
    children: [
      { path: '', component: DashboardHomeComponent },
      { path: 'af-lookup', component: AfLookupComponent },
      { path: 'update-balance', component: UpdateBalanceComponent },
      { path: 'update-faf', component: UpdateFafComponent },
      { path: 'set-grace-date', component: SetGraceDateComponent },
      { path: 'update-ua', component: UpdateUaComponent },
      { path: 'update-cug', component: UpdateCugComponent },
    ]
  }
];

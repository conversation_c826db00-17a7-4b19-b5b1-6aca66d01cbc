<?php
// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'tunisia_dashboard');
define('DB_USER', 'root');
define('DB_PASS', '');

// Création de la connexion PDO
try {
    $pdo = new PDO("mysql:host=" . DB_HOST . ";dbname=" . DB_NAME, DB_USER, DB_PASS);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
} catch(PDOException $e) {
    die("Erreur de connexion : " . $e->getMessage());
}
?> 